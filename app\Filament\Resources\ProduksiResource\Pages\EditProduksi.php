<?php

namespace App\Filament\Resources\ProduksiResource\Pages;

use App\Filament\Resources\ProduksiResource;
use App\Models\Produksi;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditProduksi extends EditRecord
{
    protected static string $resource = ProduksiResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing production data for this period
        $produksiData = [];
        $existingProduksi = Produksi::where('periode', $this->record->periode)->get();

        foreach ($existingProduksi as $produksi) {
            $produksiData[$produksi->produk_id] = [
                'jumlah' => $produksi->jumlah,
                'satuan' => $produksi->satuan,
            ];
        }

        $data['produksi_data'] = $produksiData;
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Update all production records for this period
        foreach ($data['produksi_data'] as $produkId => $values) {
            Produksi::updateOrCreate(
                [
                    'periode' => $data['periode'],
                    'produk_id' => $produkId
                ],
                [
                    'jumlah' => $values['jumlah'],
                    'satuan' => $values['satuan'],
                ]
            );
        }

        // Return the first record for the period
        return Produksi::where('periode', $data['periode'])->first();
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Produksi')
                ->modalDescription('Apakah Anda yakin ingin menghapus semua data produksi untuk periode ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal')
                ->action(function () {
                    Produksi::where('periode', $this->record->periode)->delete();
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Edit Data Produksi';
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}