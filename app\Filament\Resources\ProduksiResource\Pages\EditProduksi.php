<?php

namespace App\Filament\Resources\ProduksiResource\Pages;

use App\Filament\Resources\ProduksiResource;
use App\Models\Produksi;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditProduksi extends EditRecord
{
    protected static string $resource = ProduksiResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing production data for this period
        $produksiData = [];
        $existingProduksi = Produksi::where('periode', $this->record->periode)->get();

        foreach ($existingProduksi as $produksi) {
            $produksiData[$produksi->produk_id] = [
                'jumlah' => $produksi->jumlah,
                'satuan' => $produksi->satuan,
            ];
        }

        $data['produksi_data'] = $produksiData;
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $produksiData = $data['produksi_data'] ?? [];
        $oldPeriode = $record->periode;
        $newPeriode = \Carbon\Carbon::parse($data['periode'])->startOfMonth();

        // If trying to change to a different period
        if ($oldPeriode->format('Y-m') !== $newPeriode->format('Y-m')) {
            // Check if the new period already exists
            $existingRecord = Produksi::where('periode', 'LIKE', $newPeriode->format('Y-m').'%')
                ->where('id', '!=', $record->id)
                ->first();

            if ($existingRecord) {
                Notification::make()
                    ->danger()
                    ->title('Periode Sudah Ada')
                    ->body('Data produksi untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                    ->persistent()
                    ->send();

                $this->halt();
            }
        }

        // Update all related records
        foreach ($produksiData as $produkId => $values) {
            Produksi::where('periode', $oldPeriode->format('Y-m-d'))
                ->where('produk_id', $produkId)
                ->update([
                    'periode' => $newPeriode->format('Y-m-d'),
                    'jumlah' => (int)($values['jumlah'] ?? 0),
                    'satuan' => $values['satuan'] ?? 'pcs',
                ]);
        }

        return $record->fresh();
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Produksi')
                ->modalDescription('Apakah Anda yakin ingin menghapus semua data produksi untuk periode ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal')
                ->action(function () {
                    Produksi::where('periode', $this->record->periode)->delete();
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Edit Data Produksi';
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}