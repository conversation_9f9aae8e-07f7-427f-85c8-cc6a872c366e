<?php

namespace App\Filament\Resources\ProduksiResource\Pages;

use App\Filament\Resources\ProduksiResource;
use App\Models\Produksi;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditProduksi extends EditRecord
{
    protected static string $resource = ProduksiResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load data for ALL active products, not just existing production records
        $produksiData = [];
        $activeProducts = \App\Models\Produk::where('status', 'Aktif')->get();
        $existingProduksi = Produksi::where('periode', $this->record->periode)->get()->keyBy('produk_id');

        foreach ($activeProducts as $produk) {
            if (isset($existingProduksi[$produk->id])) {
                // Product has existing production data
                $produksiData[$produk->id] = [
                    'jumlah' => $existingProduksi[$produk->id]->jumlah,
                    'satuan' => $existingProduksi[$produk->id]->satuan,
                ];
            } else {
                // Product doesn't have production data yet, set defaults
                $produksiData[$produk->id] = [
                    'jumlah' => 0,
                    'satuan' => 'pcs',
                ];
            }
        }

        $data['produksi_data'] = $produksiData;
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $produksiData = $data['produksi_data'] ?? [];
        $oldPeriode = $record->periode;
        $newPeriode = \Carbon\Carbon::parse($data['periode'])->startOfMonth();

        // If trying to change to a different period
        if ($oldPeriode->format('Y-m') !== $newPeriode->format('Y-m')) {
            // Check if the new period already exists
            $existingRecord = Produksi::where('periode', 'LIKE', $newPeriode->format('Y-m').'%')
                ->where('id', '!=', $record->id)
                ->first();

            if ($existingRecord) {
                Notification::make()
                    ->danger()
                    ->title('Periode Sudah Ada')
                    ->body('Data produksi untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                    ->persistent()
                    ->send();

                $this->halt();
            }
        }

        // Update existing records and create new ones for new products
        foreach ($produksiData as $produkId => $values) {
            // First, delete any existing record for this product and old period
            Produksi::where('periode', $oldPeriode->format('Y-m-d'))
                ->where('produk_id', $produkId)
                ->delete();

            // Then create/update the record for the new period
            Produksi::updateOrCreate(
                [
                    'periode' => $newPeriode->format('Y-m-d'),
                    'produk_id' => $produkId
                ],
                [
                    'jumlah' => (int)($values['jumlah'] ?? 0),
                    'satuan' => $values['satuan'] ?? 'pcs',
                ]
            );
        }

        return $record->fresh();
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Produksi')
                ->modalDescription('Apakah Anda yakin ingin menghapus semua data produksi untuk periode ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal')
                ->action(function () {
                    Produksi::where('periode', $this->record->periode)->delete();
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Edit Data Produksi';
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}