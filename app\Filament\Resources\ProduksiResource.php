<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProduksiResource\Pages;
use App\Models\Produk;
use App\Models\Produksi;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProduksiResource extends Resource
{
    protected static ?string $model = Produksi::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $modelLabel = 'Produksi';
    protected static ?string $pluralModelLabel = 'Produksi';
    protected static ?string $slug = 'produksi';
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        $produks = \App\Models\Produk::where('status', 'Aktif')->get();

        return $form->schema([
            Forms\Components\DatePicker::make('periode')
                ->label('Periode')
                ->required()
                ->format('Y-m')
                ->displayFormat('F Y')
                ->native(false)
                ->columnSpanFull(),
            Forms\Components\Section::make('Data Produksi')
                ->schema(
                    $produks->chunk(2)->flatMap(function ($chunk) {
                        return [
                            Forms\Components\Grid::make()
                                ->schema(
                                    $chunk->flatMap(fn ($produk) => [
                                        Forms\Components\Section::make($produk->nama_produk)
                                            ->compact()
                                            ->collapsible(false)
                                            ->extraAttributes([
                                                'class' => 'text-center',
                                            ])
                                            ->schema([
                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\TextInput::make("produksi_data.{$produk->id}.jumlah")
                                                            ->label('Jumlah')
                                                            ->numeric()
                                                            ->default(0)
                                                            ->minValue(0)
                                                            ->step(0.1)
                                                            ->afterStateHydrated(function ($component, $state, $record) use ($produk) {
                                                                if ($record) {
                                                                    $existingProduksi = \App\Models\Produksi::where('periode', $record->periode)
                                                                        ->where('produk_id', $produk->id)
                                                                        ->first();
                                                                    $component->state($existingProduksi?->jumlah ?? 0);
                                                                }
                                                            }),
                                                        Forms\Components\TextInput::make("produksi_data.{$produk->id}.satuan")
                                                            ->label('Satuan')
                                                            ->default('pcs')
                                                            ->afterStateHydrated(function ($component, $state, $record) use ($produk) {
                                                                if ($record) {
                                                                    $existingProduksi = \App\Models\Produksi::where('periode', $record->periode)
                                                                        ->where('produk_id', $produk->id)
                                                                        ->first();
                                                                    $component->state($existingProduksi?->satuan ?? 'pcs');
                                                                }
                                                            }),
                                                    ])
                                            ])
                                            ->columnSpan(2)
                                    ])->toArray()
                                )->columns(4)
                        ];
                    })->toArray()
                )->columns(1)
        ])->statePath('data');
    }

    public static function table(Table $table): Table
    {
        $produks = \App\Models\Produk::where('status', 'Aktif')->get();

        $columns = [
            Tables\Columns\TextColumn::make('periode')
                ->label('Periode')
                ->date('F Y')
                ->sortable()
                ->searchable(query: function (\Illuminate\Database\Eloquent\Builder $query, string $search): \Illuminate\Database\Eloquent\Builder {
                    return $query->where(function (\Illuminate\Database\Eloquent\Builder $subQuery) use ($search) {
                        // Original English month/year search
                        $subQuery->orWhereRaw("DATE_FORMAT(periode, '%M %Y') LIKE ?", ["%{$search}%"])
                                 ->orWhereRaw("DATE_FORMAT(periode, '%Y') LIKE ?", ["%{$search}%"]);

                        // Indonesian month search logic
                        $indonesianMonths = [
                            'januari' => 1, 'februari' => 2, 'maret' => 3, 'april' => 4,
                            'mei' => 5, 'juni' => 6, 'juli' => 7, 'agustus' => 8,
                            'september' => 9, 'oktober' => 10, 'november' => 11, 'desember' => 12,
                        ];

                        foreach ($indonesianMonths as $indonesianMonth => $monthNumber) {
                            if (stripos($search, $indonesianMonth) !== false) {
                                $subQuery->orWhereRaw("MONTH(periode) = ?", [$monthNumber]);

                                // Extract year if present in search
                                if (preg_match('/(\d{4})/', $search, $matches)) {
                                    $year = $matches[1];
                                    $subQuery->whereRaw("YEAR(periode) = ?", [$year]);
                                }
                                break;
                            }
                        }
                    });
                }),
        ];

        foreach ($produks as $produk) {
            $columns[] = Tables\Columns\TextColumn::make("produk_{$produk->id}")
                ->label($produk->nama_produk)
                ->getStateUsing(function ($record) use ($produk) {
                    $produksi = \App\Models\Produksi::where('periode', $record->periode)
                        ->where('produk_id', $produk->id)
                        ->first();
                    if ($produksi) {
                        $jumlah = $produksi->jumlah;
                        if (fmod($jumlah, 1) == 0) {
                            $jumlah_formatted = number_format($jumlah, 0);
                        } else {
                            $jumlah_formatted = number_format($jumlah, 1);
                        }
                        return "{$jumlah_formatted} {$produksi->satuan}";
                    } else {
                        return "0 pcs";
                    }
                })
                ->numeric();
        }

        return $table
            ->columns($columns)
            ->filters([
                Filter::make('periode')
                    ->form([
                        DatePicker::make('periode_dari')
                            ->label('Dari Periode')
                            ->format('Y-m')
                            ->displayFormat('F Y')
                            ->native(false),
                        DatePicker::make('periode_sampai')
                            ->label('Sampai Periode')
                            ->format('Y-m')
                            ->displayFormat('F Y')
                            ->native(false),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('periode', '>=', $date),
                            )
                            ->when(
                                $data['periode_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('periode', '<=', $date),
                            );
                    })
            ])
            ->actions([
                EditAction::make()
                    ->label('Edit')
                    ->modalHeading('Edit Data Produksi')
                    ->modalSubmitActionLabel('Simpan')
                    ->modalCancelActionLabel('Batal'),
                DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Produksi')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data produksi ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Produksi')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data produksi yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            foreach ($records as $record) {
                                \App\Models\Produksi::where('periode', $record->periode)->forceDelete();
                            }
                        }),
                ]),
            ])
            ->modifyQueryUsing(function ($query) {
                return $query->selectRaw('MIN(id) as id, periode')
                    ->groupBy('periode');
            })
            ->defaultSort('periode', 'desc');
    }

    public static function getPages(): array // Defines the pages associated with this resource
    {
        return [
            'index' => Pages\ListProduksis::route('/'), // Route for the list page
            'create' => Pages\CreateProduksi::route('/create'), // Route for the create page
            'edit' => Pages\EditProduksi::route('/{record}/edit'), // Route for the edit page
        ];
    }
}